'use client'

import { useMutation, useQuery } from '@tanstack/react-query'
import { Link, Shield, Unlink } from 'lucide-react'
import { useState } from 'react'

import { Icons } from '@/components/icons'
import {
  AlertDialog,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { cn } from '@/lib/utils'
import { queryFetchHelper } from '@/lib/utils/fetchHelper'
import { toast } from 'sonner'

// API Response interface
interface ApiResponse<T = any> {
  success: boolean
  code: number
  locale: string
  message: string
  data?: T
}

// Social account interface
interface SocialAccount {
  id: string
  provider: string
  name: string
  avatar?: string
  account_id: string
  created_at: string
}

// Social providers configuration - Only Google supported
const socialProviders = [
  {
    id: 'google',
    name: 'Google',
    icon: Icons.google,
    color: 'bg-white border shadow-sm',
  },
] as const

export default function SocialLoginSection() {
  const [openDialogId, setOpenDialogId] = useState<string | null>(null)

  // Fetch user's social accounts
  const {
    data: socialAccounts,
    refetch: refetchSocialAccounts,
    isLoading,
    error,
  } = useQuery<ApiResponse<{ items: SocialAccount[] }>>({
    queryKey: ['socialAccounts'],
    queryFn: () => queryFetchHelper('/profile/social-accounts'),
  })

  // Social account disconnect mutation
  const { isPending: isDisconnecting, mutate: disconnectSocialAccount } = useMutation<ApiResponse, Error, string>({
    mutationFn: async (accountId: string) => {
      return queryFetchHelper(`/profile/social-accounts/${accountId}`, {
        method: 'DELETE',
      })
    },
    onSuccess: (data) => {
      toast.success(data.message || 'Tài khoản đã được ngắt kết nối')
      refetchSocialAccounts()
      setOpenDialogId(null) // Close dialog after success
    },
    onError: (error) => {
      toast.error(error.message || 'Ngắt kết nối thất bại')
      setOpenDialogId(null) // Close dialog even on error
    },
  })

  const handleDisconnectSocialAccount = (accountId: string): void => {
    disconnectSocialAccount(accountId)
  }

  return (
    <div className="space-y-4">
      <div>
        <h2 className="flex items-center gap-2 text-lg font-semibold">
          <Shield className="h-5 w-5" />
          Quản lý đăng nhập xã hội
        </h2>
        <p className="text-muted-foreground text-sm">Kết nối hoặc ngắt kết nối các tài khoản mạng xã hội của bạn</p>
      </div>
      <div>
        {isLoading ? (
          <div className="text-muted-foreground py-8 text-center">
            <Shield className="mx-auto mb-4 h-12 w-12 animate-pulse opacity-50" />
            <p>Đang tải...</p>
          </div>
        ) : error ? (
          <div className="text-muted-foreground py-8 text-center">
            <Shield className="mx-auto mb-4 h-12 w-12 opacity-50" />
            <p>Không thể tải thông tin tài khoản</p>
            <p className="text-sm">Vui lòng thử lại sau</p>
          </div>
        ) : (
          <div className="space-y-4">
            {socialProviders.map((provider) => {
              const connectedAccount = Array.isArray(socialAccounts?.data?.items)
                ? socialAccounts.data.items.find((account) => account.provider === provider.id)
                : undefined

              return (
                <div
                  key={provider.id}
                  className="flex items-center justify-between rounded-lg border p-4">
                  <div className="flex items-center gap-3">
                    <div className={cn('flex h-10 w-10 items-center justify-center rounded-full', provider.color)}>
                      <provider.icon className="h-5 w-5" />
                    </div>
                    <div>
                      <h4 className="font-medium">{provider.name}</h4>
                      {connectedAccount ? (
                        <p className="text-muted-foreground text-sm">Đã kết nối với {connectedAccount.name}</p>
                      ) : (
                        <p className="text-muted-foreground text-sm">Chưa kết nối</p>
                      )}
                    </div>
                  </div>

                  <div className="flex items-center gap-2">
                    {connectedAccount ? (
                      <>
                        <Badge
                          variant="secondary"
                          className="flex h-8 items-center gap-1">
                          <Link className="h-3 w-3" />
                          Đã kết nối
                        </Badge>
                        <AlertDialog
                          open={openDialogId === connectedAccount.id}
                          onOpenChange={(open) => setOpenDialogId(open ? connectedAccount.id : null)}>
                          <AlertDialogTrigger asChild>
                            <Button
                              variant="outline"
                              size="sm"
                              className="h-8 px-3"
                              disabled={isDisconnecting}>
                              <Unlink className="mr-1 h-3 w-3" />
                              Ngắt kết nối
                            </Button>
                          </AlertDialogTrigger>
                          <AlertDialogContent>
                            <AlertDialogHeader>
                              <AlertDialogTitle>Xác nhận ngắt kết nối</AlertDialogTitle>
                              <AlertDialogDescription>
                                Bạn có chắc chắn muốn ngắt kết nối tài khoản {provider.name} này không? Bạn sẽ không thể
                                đăng nhập bằng {provider.name} nữa.
                              </AlertDialogDescription>
                            </AlertDialogHeader>
                            <AlertDialogFooter>
                              <AlertDialogCancel disabled={isDisconnecting}>Hủy</AlertDialogCancel>
                              <Button
                                onClick={() => handleDisconnectSocialAccount(connectedAccount.id)}
                                disabled={isDisconnecting}
                                className="bg-destructive hover:bg-destructive/90 text-white">
                                {isDisconnecting ? (
                                  <>
                                    <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
                                    Đang xử lý...
                                  </>
                                ) : (
                                  'Ngắt kết nối'
                                )}
                              </Button>
                            </AlertDialogFooter>
                          </AlertDialogContent>
                        </AlertDialog>
                      </>
                    ) : (
                      <Button
                        variant="outline"
                        size="sm"
                        className="h-8 px-3">
                        <Link className="mr-1 h-3 w-3" />
                        Kết nối
                      </Button>
                    )}
                  </div>
                </div>
              )
            })}

            {(!Array.isArray(socialAccounts?.data?.items) || socialAccounts.data.items.length === 0) && (
              <div className="text-muted-foreground py-8 text-center">
                <Shield className="mx-auto mb-4 h-12 w-12 opacity-50" />
                <p>Không tìm thấy tài khoản nào</p>
                <p className="text-sm">Kết nối tài khoản Google để đăng nhập dễ dàng hơn</p>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  )
}
