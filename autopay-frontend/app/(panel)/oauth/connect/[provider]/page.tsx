'use client'

import { signIn, useSession } from 'next-auth/react'

import { Icons } from '@/components/icons'
import { useRouter } from 'next/navigation'
import { use, useEffect } from 'react'

type ProviderPageProps = Promise<{
  provider: string
}>

const ConnectSocialAccount = ({ params }: { params: ProviderPageProps }) => {
  const { data: session, status } = useSession()
  const router = useRouter()
  const { provider } = use(params)

  useEffect(() => {
    if (!window.opener) {
      return router.push('/profile/security')
    }

    if (!(status === 'loading') && !session) void signIn(provider)

    if (session) {
      if (window.opener && !window.opener.closed) {
        window.opener.postMessage(
          {
            provider,
            action: 'oauth.popup.close',
            session,
          },
          window.location.origin
        )
      }
      window.close()
    }
  }, [provider, router, session, status])

  return (
    <div className="flex h-screen items-center justify-center gap-2">
      <Icons.spinner className="h-5 w-5 animate-spin" />
      <PERSON><PERSON> kết nối...
    </div>
  )
}

export default ConnectSocialAccount
