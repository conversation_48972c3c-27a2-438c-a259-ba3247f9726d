# Test Kết Nối Social Account

## Tính năng đã triển khai

### Backend

1. ✅ **API Endpoint**: `POST /profile/social-accounts/connect/{provider}`
   - X<PERSON> lý kết nối social account mới cho user đã đăng nhập
   - Validate provider (chỉ hỗ trợ Google)
   - Kiểm tra social account đã tồn tại hay chưa
   - Tạo kết nối mới nếu hợp lệ

2. ✅ **Route**: Đã thêm route vào `modules/User/routes/api.php`

### Frontend

1. ✅ **API Route**: `/api/oauth/connect`
   - Xử lý OAuth flow cho việc kết nối social account
   - Sử dụng NextAuth session để lấy thông tin OAuth

2. ✅ **OAuth Page**: `/oauth/connect/[provider]`
   - Page riêng cho việc kết nối social account
   - Khác với OAuth page cho login/register

3. ✅ **SocialLoginSection Component**:
   - Thêm popup OAuth flow
   - Thêm mutation cho kết nối social account
   - Thêm UI loading state cho nút kết nối
   - Xử lý message event từ OAuth popup

## Cách hoạt động

1. **User click nút "Kết nối"** → Mở popup `/oauth/connect/google`
2. **OAuth popup** → Thực hiện Google OAuth flow
3. **Sau khi OAuth thành công** → Popup gửi message về parent window
4. **Parent window nhận message** → Gọi API `/api/oauth/connect`
5. **Frontend API** → Gọi backend API `/profile/social-accounts/connect/google`
6. **Backend** → Tạo kết nối social account mới
7. **Frontend** → Refresh danh sách social accounts và hiển thị thông báo

## Test Manual

### Điều kiện tiên quyết

- User đã đăng nhập vào hệ thống
- Truy cập trang `/profile/security`

### Các bước test

1. Mở trang Profile Security
2. Tìm section "Quản lý đăng nhập xã hội"
3. Click nút "Kết nối" cho Google
4. Popup OAuth sẽ mở
5. Đăng nhập Google trong popup
6. Popup sẽ tự động đóng
7. Kiểm tra xem tài khoản Google đã được kết nối hay chưa

### Kết quả mong đợi

- Tài khoản Google hiển thị trạng thái "Đã kết nối"
- Có thông báo thành công
- Có thể ngắt kết nối tài khoản

## Vấn đề đã sửa

### ✅ Middleware Issue

**Vấn đề**: Middleware chặn OAuth pages cho logged-in users
**Giải pháp**:

- Tách `oauthPaths` riêng khỏi `authPaths` trong config
- Cập nhật middleware logic để cho phép truy cập OAuth pages

### ✅ SessionProvider Issue

**Vấn đề**: OAuth connect page không có SessionProvider
**Giải pháp**: Tạo layout riêng cho `/oauth` với SessionProvider

### ✅ Page Structure

**Vấn đề**: OAuth connect page ban đầu nằm trong `(panel)` folder
**Giải pháp**: Di chuyển ra `/oauth/connect/[provider]` để tránh panel layout

## Test Results

- ✅ `/oauth/connect/google` trả về 200 OK
- ✅ Page hiển thị "Đang kết nối..."
- ✅ Middleware không chặn OAuth pages
- ✅ SessionProvider hoạt động đúng

## Lưu ý

- Chỉ hỗ trợ Google OAuth
- Một social account chỉ có thể kết nối với một user
- User có thể có nhiều social accounts khác nhau
- Cần có Google OAuth credentials được cấu hình đúng
- Tính năng đã sẵn sàng để test manual trên production
